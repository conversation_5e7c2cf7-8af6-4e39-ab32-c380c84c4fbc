import asyncio
import os
import uuid
from pathlib import Path
from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException

app = FastAPI(debug=True)

# Global asyncio queue for file processing tasks
file_queue = asyncio.Queue()

@app.get("/")
def read_root():
    return {"message": "Hello, FastAP<PERSON>!"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...), user_id: str = None):
    """
    Upload endpoint that accepts files and enqueues processing tasks
    """
    try:
        # Create uploads directory if it doesn't exist
        upload_dir = Path("tmp/uploads")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename to avoid conflicts
        file_extension = Path(file.filename).suffix if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # Save the uploaded file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Create task data
        task_data = {
            "file_path": str(file_path),
            "original_filename": file.filename,
            "user_id": user_id,
            "file_size": len(content),
            "content_type": file.content_type
        }

        # Enqueue the task
        await file_queue.put(task_data)

        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "saved_as": unique_filename,
            "file_path": str(file_path),
            "user_id": user_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
